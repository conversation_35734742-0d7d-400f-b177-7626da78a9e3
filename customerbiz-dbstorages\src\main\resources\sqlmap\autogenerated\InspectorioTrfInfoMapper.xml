<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist.InspectorioTrfInfoMapper" >
  <resultMap id="BaseResultMap" type="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO" >
    <id column="id" property="id" jdbcType="BIGINT" />
    <result column="uuid" property="uuid" jdbcType="VARCHAR" />
    <result column="trf_no" property="trfNo" jdbcType="VARCHAR" />
    <result column="ref_system_id" property="refSystemId" jdbcType="INTEGER" />
    <result column="created_at" property="createdAt" jdbcType="TIMESTAMP" />
    <result column="updated_at" property="updatedAt" jdbcType="TIMESTAMP" />
    <result column="trf_status" property="trfStatus" jdbcType="VARCHAR" />
    <result column="raw_data" property="rawData" jdbcType="OTHER" />
    <result column="active_indicator" property="activeIndicator" jdbcType="TINYINT" />
    <result column="created_date" property="createdDate" jdbcType="TIMESTAMP" />
    <result column="modified_date" property="modifiedDate" jdbcType="TIMESTAMP" />
  </resultMap>
  <sql id="Example_Where_Clause" >
    <where >
      <foreach collection="oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause" >
    <where >
      <foreach collection="example.oredCriteria" item="criteria" separator="or" >
        <if test="criteria.valid" >
          <trim prefix="(" suffix=")" prefixOverrides="and" >
            <foreach collection="criteria.criteria" item="criterion" >
              <choose >
                <when test="criterion.noValue" >
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue" >
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue" >
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue" >
                  and ${criterion.condition}
                  <foreach collection="criterion.value" item="listItem" open="(" close=")" separator="," >
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List" >
    id, uuid, trf_no, ref_system_id, created_at, updated_at, trf_status, raw_data, active_indicator, 
    created_date, modified_date
  </sql>
  <select id="selectByExample" resultMap="BaseResultMap" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoExample" >
    select
    <if test="distinct" >
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_inspectorio_trf_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null" >
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" resultMap="BaseResultMap" parameterType="java.lang.Long" >
    select 
    <include refid="Base_Column_List" />
    from tb_inspectorio_trf_info
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long" >
    delete from tb_inspectorio_trf_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoExample" >
    delete from tb_inspectorio_trf_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO" >
    insert into tb_inspectorio_trf_info (id, uuid, trf_no, 
      ref_system_id, created_at, updated_at, 
      trf_status, raw_data, active_indicator, 
      created_date, modified_date)
    values (#{id,jdbcType=BIGINT}, #{uuid,jdbcType=VARCHAR}, #{trfNo,jdbcType=VARCHAR}, 
      #{refSystemId,jdbcType=INTEGER}, #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, 
      #{trfStatus,jdbcType=VARCHAR}, #{rawData,jdbcType=OTHER}, #{activeIndicator,jdbcType=TINYINT}, 
      #{createdDate,jdbcType=TIMESTAMP}, #{modifiedDate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO" >
    insert into tb_inspectorio_trf_info
    <trim prefix="(" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        id,
      </if>
      <if test="uuid != null" >
        uuid,
      </if>
      <if test="trfNo != null" >
        trf_no,
      </if>
      <if test="refSystemId != null" >
        ref_system_id,
      </if>
      <if test="createdAt != null" >
        created_at,
      </if>
      <if test="updatedAt != null" >
        updated_at,
      </if>
      <if test="trfStatus != null" >
        trf_status,
      </if>
      <if test="rawData != null" >
        raw_data,
      </if>
      <if test="activeIndicator != null" >
        active_indicator,
      </if>
      <if test="createdDate != null" >
        created_date,
      </if>
      <if test="modifiedDate != null" >
        modified_date,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides="," >
      <if test="id != null" >
        #{id,jdbcType=BIGINT},
      </if>
      <if test="uuid != null" >
        #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="trfNo != null" >
        #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="refSystemId != null" >
        #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null" >
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null" >
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="trfStatus != null" >
        #{trfStatus,jdbcType=VARCHAR},
      </if>
      <if test="rawData != null" >
        #{rawData,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoExample" resultType="java.lang.Integer" >
    select count(*) from tb_inspectorio_trf_info
    <if test="_parameter != null" >
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map" >
    update tb_inspectorio_trf_info
    <set >
      <if test="record.id != null" >
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.uuid != null" >
        uuid = #{record.uuid,jdbcType=VARCHAR},
      </if>
      <if test="record.trfNo != null" >
        trf_no = #{record.trfNo,jdbcType=VARCHAR},
      </if>
      <if test="record.refSystemId != null" >
        ref_system_id = #{record.refSystemId,jdbcType=INTEGER},
      </if>
      <if test="record.createdAt != null" >
        created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updatedAt != null" >
        updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.trfStatus != null" >
        trf_status = #{record.trfStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.rawData != null" >
        raw_data = #{record.rawData,jdbcType=OTHER},
      </if>
      <if test="record.activeIndicator != null" >
        active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="record.createdDate != null" >
        created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="record.modifiedDate != null" >
        modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map" >
    update tb_inspectorio_trf_info
    set id = #{record.id,jdbcType=BIGINT},
      uuid = #{record.uuid,jdbcType=VARCHAR},
      trf_no = #{record.trfNo,jdbcType=VARCHAR},
      ref_system_id = #{record.refSystemId,jdbcType=INTEGER},
      created_at = #{record.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{record.updatedAt,jdbcType=TIMESTAMP},
      trf_status = #{record.trfStatus,jdbcType=VARCHAR},
      raw_data = #{record.rawData,jdbcType=OTHER},
      active_indicator = #{record.activeIndicator,jdbcType=TINYINT},
      created_date = #{record.createdDate,jdbcType=TIMESTAMP},
      modified_date = #{record.modifiedDate,jdbcType=TIMESTAMP}
    <if test="_parameter != null" >
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO" >
    update tb_inspectorio_trf_info
    <set >
      <if test="uuid != null" >
        uuid = #{uuid,jdbcType=VARCHAR},
      </if>
      <if test="trfNo != null" >
        trf_no = #{trfNo,jdbcType=VARCHAR},
      </if>
      <if test="refSystemId != null" >
        ref_system_id = #{refSystemId,jdbcType=INTEGER},
      </if>
      <if test="createdAt != null" >
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null" >
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="trfStatus != null" >
        trf_status = #{trfStatus,jdbcType=VARCHAR},
      </if>
      <if test="rawData != null" >
        raw_data = #{rawData,jdbcType=OTHER},
      </if>
      <if test="activeIndicator != null" >
        active_indicator = #{activeIndicator,jdbcType=TINYINT},
      </if>
      <if test="createdDate != null" >
        created_date = #{createdDate,jdbcType=TIMESTAMP},
      </if>
      <if test="modifiedDate != null" >
        modified_date = #{modifiedDate,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO" >
    update tb_inspectorio_trf_info
    set uuid = #{uuid,jdbcType=VARCHAR},
      trf_no = #{trfNo,jdbcType=VARCHAR},
      ref_system_id = #{refSystemId,jdbcType=INTEGER},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      trf_status = #{trfStatus,jdbcType=VARCHAR},
      raw_data = #{rawData,jdbcType=OTHER},
      active_indicator = #{activeIndicator,jdbcType=TINYINT},
      created_date = #{createdDate,jdbcType=TIMESTAMP},
      modified_date = #{modifiedDate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="batchInsert" parameterType="list" >
    insert into tb_inspectorio_trf_info
      (`id`,`uuid`,`trf_no`,
      `ref_system_id`,`created_at`,`updated_at`,
      `trf_status`,`raw_data`,`active_indicator`,
      `created_date`,`modified_date`)
    values 
    <foreach collection="list" item="item" index="index" separator="," > 
      ( #{ item.id, jdbcType=BIGINT},#{ item.uuid, jdbcType=VARCHAR},#{ item.trfNo, jdbcType=VARCHAR},
      #{ item.refSystemId, jdbcType=INTEGER},#{ item.createdAt, jdbcType=TIMESTAMP},#{ item.updatedAt, jdbcType=TIMESTAMP},
      #{ item.trfStatus, jdbcType=VARCHAR},#{ item.rawData, jdbcType=OTHER},#{ item.activeIndicator, jdbcType=TINYINT},
      #{ item.createdDate, jdbcType=TIMESTAMP},#{ item.modifiedDate, jdbcType=TIMESTAMP}) 
    </foreach>
  </insert>
  <update id="batchUpdate" parameterType="list" >
    <foreach collection="list" item="item" index="index" separator=";" > 
      update tb_inspectorio_trf_info 
      <set>
        <if test="item.uuid != null"> 
          `uuid` = #{item.uuid, jdbcType = VARCHAR},
        </if> 
        <if test="item.trfNo != null"> 
          `trf_no` = #{item.trfNo, jdbcType = VARCHAR},
        </if> 
        <if test="item.refSystemId != null"> 
          `ref_system_id` = #{item.refSystemId, jdbcType = INTEGER},
        </if> 
        <if test="item.createdAt != null"> 
          `created_at` = #{item.createdAt, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.updatedAt != null"> 
          `updated_at` = #{item.updatedAt, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.trfStatus != null"> 
          `trf_status` = #{item.trfStatus, jdbcType = VARCHAR},
        </if> 
        <if test="item.rawData != null"> 
          `raw_data` = #{item.rawData, jdbcType = OTHER},
        </if> 
        <if test="item.activeIndicator != null"> 
          `active_indicator` = #{item.activeIndicator, jdbcType = TINYINT},
        </if> 
        <if test="item.createdDate != null"> 
          `created_date` = #{item.createdDate, jdbcType = TIMESTAMP},
        </if> 
        <if test="item.modifiedDate != null"> 
          `modified_date` = #{item.modifiedDate, jdbcType = TIMESTAMP},
        </if> 
      </set>
      <where>
        <if test="item.id != null">
           and `id` = #{item.id,jdbcType = BIGINT}
        </if>
      </where>
    </foreach>
  </update>
</mapper>