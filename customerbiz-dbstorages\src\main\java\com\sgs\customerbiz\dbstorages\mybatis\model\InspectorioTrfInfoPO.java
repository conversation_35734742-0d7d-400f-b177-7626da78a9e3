package com.sgs.customerbiz.dbstorages.mybatis.model;

import java.util.Date;

public class InspectorioTrfInfoPO {
    /**
     * id BIGINT(19) 必填<br>
     * 主键ID
     */
    private Long id;

    /**
     * uuid VARCHAR(64) 必填<br>
     * Customer UUID (uuid)
     */
    private String uuid;

    /**
     * trf_no VARCHAR(128) 必填<br>
     * Customer TRF No (labTestId)
     */
    private String trfNo;

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * 系统ID(10030=Lululemon, 10028=Target)
     */
    private Integer refSystemId;

    /**
     * created_at TIMESTAMP(19)<br>
     * Customer createdAt
     */
    private Date createdAt;

    /**
     * updated_at TIMESTAMP(19)<br>
     * Customer updatedAt
     */
    private Date updatedAt;

    /**
     * trf_status VARCHAR(32)<br>
     * Customer status
     */
    private String trfStatus;

    /**
     * raw_data OTHER 必填<br>
     * 原始JSON数据
     */
    private Object rawData;

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 
     */
    private Integer activeIndicator;

    /**
     * created_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 
     */
    private Date createdDate;

    /**
     * modified_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 
     */
    private Date modifiedDate;

    /**
     * id BIGINT(19) 必填<br>
     * 获得 主键ID
     */
    public Long getId() {
        return id;
    }

    /**
     * id BIGINT(19) 必填<br>
     * 设置 主键ID
     */
    public void setId(Long id) {
        this.id = id;
    }

    /**
     * uuid VARCHAR(64) 必填<br>
     * 获得 Customer UUID (uuid)
     */
    public String getUuid() {
        return uuid;
    }

    /**
     * uuid VARCHAR(64) 必填<br>
     * 设置 Customer UUID (uuid)
     */
    public void setUuid(String uuid) {
        this.uuid = uuid == null ? null : uuid.trim();
    }

    /**
     * trf_no VARCHAR(128) 必填<br>
     * 获得 Customer TRF No (labTestId)
     */
    public String getTrfNo() {
        return trfNo;
    }

    /**
     * trf_no VARCHAR(128) 必填<br>
     * 设置 Customer TRF No (labTestId)
     */
    public void setTrfNo(String trfNo) {
        this.trfNo = trfNo == null ? null : trfNo.trim();
    }

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * 获得 系统ID(10030=Lululemon, 10028=Target)
     */
    public Integer getRefSystemId() {
        return refSystemId;
    }

    /**
     * ref_system_id INTEGER(10) 必填<br>
     * 设置 系统ID(10030=Lululemon, 10028=Target)
     */
    public void setRefSystemId(Integer refSystemId) {
        this.refSystemId = refSystemId;
    }

    /**
     * created_at TIMESTAMP(19)<br>
     * 获得 Customer createdAt
     */
    public Date getCreatedAt() {
        return createdAt;
    }

    /**
     * created_at TIMESTAMP(19)<br>
     * 设置 Customer createdAt
     */
    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    /**
     * updated_at TIMESTAMP(19)<br>
     * 获得 Customer updatedAt
     */
    public Date getUpdatedAt() {
        return updatedAt;
    }

    /**
     * updated_at TIMESTAMP(19)<br>
     * 设置 Customer updatedAt
     */
    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    /**
     * trf_status VARCHAR(32)<br>
     * 获得 Customer status
     */
    public String getTrfStatus() {
        return trfStatus;
    }

    /**
     * trf_status VARCHAR(32)<br>
     * 设置 Customer status
     */
    public void setTrfStatus(String trfStatus) {
        this.trfStatus = trfStatus == null ? null : trfStatus.trim();
    }

    /**
     * raw_data OTHER 必填<br>
     * 获得 原始JSON数据
     */
    public Object getRawData() {
        return rawData;
    }

    /**
     * raw_data OTHER 必填<br>
     * 设置 原始JSON数据
     */
    public void setRawData(Object rawData) {
        this.rawData = rawData;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 获得 
     */
    public Integer getActiveIndicator() {
        return activeIndicator;
    }

    /**
     * active_indicator TINYINT(3) 默认值[1] 必填<br>
     * 设置 
     */
    public void setActiveIndicator(Integer activeIndicator) {
        this.activeIndicator = activeIndicator;
    }

    /**
     * created_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 获得 
     */
    public Date getCreatedDate() {
        return createdDate;
    }

    /**
     * created_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 设置 
     */
    public void setCreatedDate(Date createdDate) {
        this.createdDate = createdDate;
    }

    /**
     * modified_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 获得 
     */
    public Date getModifiedDate() {
        return modifiedDate;
    }

    /**
     * modified_date TIMESTAMP(19) 默认值[CURRENT_TIMESTAMP]<br>
     * 设置 
     */
    public void setModifiedDate(Date modifiedDate) {
        this.modifiedDate = modifiedDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append(getClass().getSimpleName());
        sb.append(" [");
        sb.append("Hash = ").append(hashCode());
        sb.append(", id=").append(id);
        sb.append(", uuid=").append(uuid);
        sb.append(", trfNo=").append(trfNo);
        sb.append(", refSystemId=").append(refSystemId);
        sb.append(", createdAt=").append(createdAt);
        sb.append(", updatedAt=").append(updatedAt);
        sb.append(", trfStatus=").append(trfStatus);
        sb.append(", rawData=").append(rawData);
        sb.append(", activeIndicator=").append(activeIndicator);
        sb.append(", createdDate=").append(createdDate);
        sb.append(", modifiedDate=").append(modifiedDate);
        sb.append("]");
        return sb.toString();
    }
}