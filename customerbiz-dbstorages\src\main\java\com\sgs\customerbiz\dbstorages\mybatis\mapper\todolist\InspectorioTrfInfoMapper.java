package com.sgs.customerbiz.dbstorages.mybatis.mapper.todolist;

import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoExample;
import com.sgs.customerbiz.dbstorages.mybatis.model.InspectorioTrfInfoPO;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface InspectorioTrfInfoMapper {
    int countByExample(InspectorioTrfInfoExample example);

    int deleteByExample(InspectorioTrfInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(InspectorioTrfInfoPO record);

    int insertSelective(InspectorioTrfInfoPO record);

    List<InspectorioTrfInfoPO> selectByExample(InspectorioTrfInfoExample example);

    InspectorioTrfInfoPO selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") InspectorioTrfInfoPO record, @Param("example") InspectorioTrfInfoExample example);

    int updateByExample(@Param("record") InspectorioTrfInfoPO record, @Param("example") InspectorioTrfInfoExample example);

    int updateByPrimaryKeySelective(InspectorioTrfInfoPO record);

    int updateByPrimaryKey(InspectorioTrfInfoPO record);

    int batchInsert(List<InspectorioTrfInfoPO> list);

    int batchUpdate(List<InspectorioTrfInfoPO> list);
}