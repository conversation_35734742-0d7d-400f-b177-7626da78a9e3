package com.sgs.customerbiz.integration;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.module.SimpleModule;
import com.fasterxml.jackson.databind.node.JsonNodeType;
import com.sgs.config.api.dto.DFFMappingDTO;
import com.sgs.config.api.dto.req.ConfigGetReq;
import com.sgs.config.api.dto.req.DFFMappingQuery;
import com.sgs.config.api.service.DFFMappingService;
import com.sgs.customerbiz.core.config.InterfaceConfig;
import com.sgs.customerbiz.core.constants.Constants;
import com.sgs.customerbiz.core.serialize.CustomDateDeserializer;
import com.sgs.customerbiz.core.util.HttpClientUtil;
import com.sgs.customerbiz.facade.model.req.ConvertDataBodyReq;
import com.sgs.customerbiz.facade.model.req.ConvertDataReq;
import com.sgs.customerbiz.facade.model.req.QueryTestLineMappingReq;
import com.sgs.customerbiz.facade.model.req.SyncTrfInfoReq;
import com.sgs.customerbiz.facade.model.rsp.QueryTestLineMappingRsp;
import com.sgs.customerbiz.facade.model.todolist.req.TrfuiSettingReq;
import com.sgs.customerbiz.integration.dto.*;
import com.sgs.customerbiz.integration.dto.inspectorio.ResultData;
import com.sgs.customerbiz.integration.dto.inspectorio.Revisable;
import com.sgs.customerbiz.integration.dto.inspectorio.RevisableData;
import com.sgs.customerbiz.model.ext.dto.req.DffInfoReq;
import com.sgs.customerbiz.model.ext.dto.req.MappingBasicDataReq;
import com.sgs.customerbiz.model.ext.dto.req.NewCheckTestLineMappingReq;
import com.sgs.customerbiz.model.ext.dto.req.QueryStarLimsFieldMapping;
import com.sgs.customerbiz.model.ext.dto.rsp.GetBasicDataMappingRsp;
import com.sgs.customerbiz.model.ext.dto.rsp.NewCheckTestLineMappingRsp;
import com.sgs.customerbiz.model.ext.dto.rsp.QueryDffDefaultRsp;
import com.sgs.customerbiz.model.ext.dto.rsp.StarLimsFieldMapping;
import com.sgs.extsystem.facade.model.customer.req.CheckTestLineMappingReq;
import com.sgs.extsystem.facade.model.customer.rsp.CheckTestLineMappingRsp;
import com.sgs.extsystem.facade.model.customer.rsp.SheinCategoryInfo;
import com.sgs.extsystem.facade.model.customer.rsp.SheinCategoryRsp;
import com.sgs.framework.core.base.BaseResponse;
import com.sgs.framework.core.base.CustomResult;
import com.sgs.framework.core.base.ResponseCode;
import com.sgs.framework.core.exception.BizException;
import com.sgs.framework.model.enums.RefSystemIdEnum;
import com.sgs.framework.tool.utils.Func;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.*;

@Component
public final class LocalILayerClient {

    private static final Logger logger = LoggerFactory.getLogger(LocalILayerClient.class);
    private static ObjectMapper objectMapper;

    @Autowired
    private DFFMappingService dffMappingService;

    static {
        objectMapper = new ObjectMapper();

        objectMapper.enable(MapperFeature.ACCEPT_CASE_INSENSITIVE_PROPERTIES);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);

        // 解析.NET客戶端传入的时间格式
        objectMapper.configure(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS, false);
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

        SimpleModule module = new SimpleModule();
        module.addDeserializer(Date.class, new CustomDateDeserializer());
        objectMapper.registerModule(module);
        objectMapper.setDateFormat(dateFormat);
    }

    @Autowired
    InterfaceConfig interfaceConfig;

    public BaseResponse getTRFUISetting(TrfuiSettingReq trfuiSettingReq) {
        try {
            String url = interfaceConfig.getLocaliLayerUrl() + Constants.GET_TRF_UI_SETTING;
            BaseResponse result = HttpClientUtil.post(url, trfuiSettingReq, BaseResponse.class);
            if (!result.isSuccess()) {
                logger.error("LocaliLayerClient.getTRFUISetting error : {}", result.getMessage());
            }
            return result;
        } catch (Exception e) {
            logger.error("LocaliLayerClient.getTRFUISetting error : {}", e.getMessage(), e);
        }
        return BaseResponse.newFailInstance("LocaliLayerClient.getTRFUISetting error");
    }

    /**
     * @param reqObject
     * @return
     */
    public BaseResponse syncGetInfo(Object reqObject) {
        return this.syncGetInfo(reqObject, null);
    }

    public BaseResponse syncGetInfo(Object reqObject, Integer timeout) {
        try {
            String url = interfaceConfig.getLocaliLayerUrl() + Constants.GET_ILayer_TRF_INFO_URL;
//            String url = "http://cnlocalilayer.sgs.net" + Constants.GET_ILayer_TRF_INFO_URL;
            BaseResponse result = HttpClientUtil.post(url, reqObject, BaseResponse.class, timeout);
            // TODO 空指针判断
            if (!result.isSuccess()) {
                logger.error("获取Trf信息失败 : {}", result.getMessage());
            }
            return result;
        } catch (Exception e) {
            logger.error("获取Trf信息失败 : {}", e.getMessage(), e);
        }

        throw new BizException("获取Trf信息失败!");
    }

    public void rewriteRefSystemId(TrfuiSettingReq reqData) {
        logger.info("rewrite refSystemId is SGSMart(1) and clear customerGroupCode. convertRequest {}", reqData);
        reqData.setRefSystemId(1);
        reqData.setCustomerGroupCode(null);
    }

    public void rewriteRefSystemId(ConvertDataReq reqData, Integer refSystemId, String customerGroupCode) {
        logger.info("rewrite refSystemId is SGSMart(1) and clear customerGroupCode. convertRequest {}", reqData);
        reqData.setRefSystemId(refSystemId);
        reqData.setCustomerGroupCode(customerGroupCode);
    }

    public void rewriteRefSystemId(ConvertDataReq reqData) {
        rewriteRefSystemId(reqData, 1, null);
    }

    /**
     * @param reqObject
     * @param trfDatas
     * @return
     */
    public JsonNode convertData(ConvertDataReq reqObject, List<Object> trfDatas) {
        // iLayer 新结构
        ConvertDataBodyReq convertDataBodyReq = new ConvertDataBodyReq();
        convertDataBodyReq.setTrfDatas(trfDatas);
        if (StringUtils.isBlank(reqObject.getCustomerGroupCode())) {
            reqObject.setCustomerGroupCode(StringUtils.EMPTY);
        }
        reqObject.setBody(convertDataBodyReq);
        return this.convertData(reqObject);
    }

    /**
     * @param convertDataReq
     * @return
     */
    public JsonNode convertData(ConvertDataReq convertDataReq) {
        try {
            String url = interfaceConfig.getLocaliLayerUrl() + Constants.FUNC_CONVERT_DATA;
            String jsonResult = HttpClientUtil.post(url, convertDataReq);
            logger.info("POST {} request={}, response={}", url, JSON.toJSONString(convertDataReq), jsonResult);
            if (Func.isBlank(jsonResult)) {
                return null;
            }
            JsonNode jsonNode = objectMapper.readTree(jsonResult);
            //.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES,false)
            //.configure(MapperFeature.SORT_PROPERTIES_ALPHABETICALLY,true)
            //.enable(SerializationFeature.INDENT_OUTPUT);

            /*JsonNode status = jsonNode.findValue("status");
            JsonNode message = jsonNode.findValue("message");*/
            JsonNode data = jsonNode.findValue("data");
            JsonNode status = jsonNode.findValue("status");

            if (Func.isEmpty(data) || Objects.equals(data.toString(), "null") || !Objects.equals(status.toString(), String.valueOf(ResponseCode.SUCCESS.getCode()))) {
                return null;
            }

            return data.getNodeType() == JsonNodeType.NULL ? null : data;
        } catch (Exception e) {
            logger.error("LocaliLayerClient.convertData error", e);
            logger.info("LocaliLayerClient.convertData error, convertDataReq={}", JSON.toJSONString(convertDataReq));
        }
        return null;
    }


    public CustomResult<List<CategoryPO>> getSheinCategoryInfoList() {
        CustomResult rspResult = new CustomResult();
        String url = String.format("%s/openapi/Sync/getSheinCategoryInfoList?languageId=1", interfaceConfig.getLocaliLayerUrl());
        BaseResponse response = HttpClientUtil.doPost(url, null, BaseResponse.class);
        if (response.getStatus() != 200) {
            logger.error("CustomerClient.getSheinCategoryInfoList 异常，异常信息{}", response.getMessage());
            return rspResult.fail(" localilayer:" + response.getMessage());
        }
        SheinCategoryRsp rsp = JSONObject.parseObject(JSONObject.toJSONString(response), SheinCategoryRsp.class);
        if (Objects.isNull(rsp.getData())) {
            logger.info("CustomerClient.getSheinCategoryInfoList 返回数据data为空,返回response:{},转换后数据:{}", JSONObject.toJSONString(response), JSONObject.toJSONString(rsp));
            return rspResult;
        }
        List<SheinCategoryInfo> categoryInfos = rsp.getData().getList();
        rspResult.setSuccess(true);
        rspResult.setData(categoryInfos);
        return rspResult;
    }

    public BaseResponse<?> rejectInspectorio(Object reqObject) {
        String result = process(reqObject);
        BaseResponse baseResponse = JSON.parseObject(result, BaseResponse.class);
        if(baseResponse.isFail()) {
            throw new IllegalStateException("call " + String.format("%s/openapi/sync/process", interfaceConfig.getLocaliLayerUrl()) + " got an error : " +baseResponse.getMessage());
        }
        return baseResponse;
    }

    public String process(Object reqObject) {
        BaseResponse<String> baseResponse = new BaseResponse<String>();
        String url = String.format("%s/openapi/sync/process", interfaceConfig.getLocaliLayerUrl());
        try {
            String fullJSON = objectMapper.writeValueAsString(reqObject);
            String result = HttpClientUtil.postJson(url, fullJSON);
            if (Func.isEmpty(result)) {
                throw new IllegalStateException(url + " got an error request" + objectMapper.writeValueAsString(reqObject));
            }
            return result;
        } catch (Exception e) {
            try {
                throw new IllegalStateException(url + " got an error: " +e.getMessage()+ " request" + objectMapper.writeValueAsString(reqObject));
            } catch (JsonProcessingException ex) {
                throw new IllegalStateException(url + " got an error request" + ex.getMessage());
            }
        }
    }

    public BaseResponse<String> createTrf(Object reqObject) {
        BaseResponse<String> baseResponse = new BaseResponse<String>();
        String url = String.format("%s/openapi/sync/process", interfaceConfig.getLocaliLayerUrl());
        try {
            String fullJSON = objectMapper.writeValueAsString(reqObject);
            String result = HttpClientUtil.postJson(url, fullJSON);
            if (Func.isEmpty(result)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
                return baseResponse;
            }
            BaseResponse<TrfResult> btr = objectMapper.readValue(result, new TypeReference<BaseResponse<TrfResult>>() {
            });
            if (Func.isEmpty(btr)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
                return baseResponse;
            }
            if (!Func.equalsSafe(ResponseCode.SUCCESS.getCode(), btr.getStatus()) || Func.isEmpty(btr.getData())) {
                baseResponse = BaseResponse.newFailInstance(org.apache.commons.lang.StringUtils.defaultString(btr.getMessage(), "请求createTrf接口报错"));
                return baseResponse;
            }
            TrfResult trf = btr.getData();
            if (Func.isEmpty(trf) || Func.isEmpty(trf.getData())) {
                String message = org.apache.commons.lang.StringUtils.defaultString(trf.getMessage(), trf.getMsg());
                baseResponse = BaseResponse.newFailInstance(org.apache.commons.lang.StringUtils.defaultString(message, "请求createTrf接口报错"));
                return baseResponse;
            }
            Object data = trf.getData();
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(data));
            Object trfNoObject = jsonObject.get(RefSystemIdEnum.SGSMart.getTrfKey());
            baseResponse.setData(Func.isEmpty(trfNoObject) ? null : trfNoObject.toString());
            return baseResponse;
        } catch (Exception e) {
            baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
            return baseResponse;
        }

    }


    public BaseResponse<String> createTrfByCustomerTrf(Object reqObject) {
        BaseResponse<String> baseResponse = new BaseResponse<String>();
        String url = String.format("%s/openapi/sync/process", interfaceConfig.getLocaliLayerUrl());
        try {
            String fullJSON = objectMapper.writeValueAsString(reqObject);
            String result = HttpClientUtil.postJson(url, fullJSON);
            if (Func.isEmpty(result)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
                return baseResponse;
            }
            BaseResponse<TrfResult> btr = objectMapper.readValue(result, new TypeReference<BaseResponse<TrfResult>>() {
            });
            if (Func.isEmpty(btr)) {
                baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
                return baseResponse;
            }
            if (!Func.equalsSafe(ResponseCode.SUCCESS.getCode(), btr.getStatus()) || Func.isEmpty(btr.getData())) {
                baseResponse = BaseResponse.newFailInstance(org.apache.commons.lang.StringUtils.defaultString(btr.getMessage(), "请求createTrf接口报错"));
                return baseResponse;
            }
            TrfResult trf = btr.getData();
            if (Func.isEmpty(trf) || Func.isEmpty(trf.getData())) {
                String message = org.apache.commons.lang.StringUtils.defaultString(trf.getMessage(), trf.getMsg());
                baseResponse = BaseResponse.newFailInstance(org.apache.commons.lang.StringUtils.defaultString(message, "请求createTrf接口报错"));
                return baseResponse;
            }
            Object data = trf.getData();
            JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(data));
            Object trfNoObject = jsonObject.get(RefSystemIdEnum.SGSMart.getTrfKey());
            baseResponse.setData(Func.isEmpty(trfNoObject) ? null : trfNoObject.toString());
            return baseResponse;
        } catch (Exception e) {
            baseResponse = BaseResponse.newFailInstance("请求createTrf接口报错");
            return baseResponse;
        }

    }
    public CpResult queryCpData(Integer refSystemId, String trfNo, String productLineCode) {
        SyncTrfInfoReq req = new SyncTrfInfoReq();
        req.setRefSystemId(refSystemId);
        req.setProductLineCode(productLineCode);
        req.setTrfNo(trfNo);
        req.setAction("GetDollarTreeCPTrfInfo");
        BaseResponse<List<CpResult>> baseResponse = new BaseResponse<List<CpResult>>();
        String url = String.format("%s/openapi/sync/getInfo", interfaceConfig.getLocaliLayerUrl());
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(url, req, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            return JSONObject.parseObject(JSONPath.eval(response, "$.data.detail.requestList[0]").toString(),CpResult.class);
        } catch (Exception e) {
            baseResponse = BaseResponse.newFailInstance("请求queryCpData接口报错");
            return null;
        }
    }


    public CpResult CpResult(Integer refSystemId, String trfNo, String productLineCode) {

        String url = "https://cnsgsmart-uat.sgs.net/api/sgs-mart/open/metadata/query/cp";
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("refSystemId", refSystemId.toString());
        reqMap.put("cpNo", trfNo);
        String fullJSON = null;
        try {
            fullJSON = objectMapper.writeValueAsString(reqMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(url, reqMap, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            return JSONArray.parseObject(JSONPath.eval(response, "$.data.requestList[0]").toString(), CpResult.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public PoResult pOResult(Integer refSystemId, String trfNo, String productLineCode) {

        String url = "https://cnsgsmart-uat.sgs.net/api/sgs-mart/open/metadata/query/po";
        Map<String, String> reqMap = new HashMap<>();
        reqMap.put("refSystemId", refSystemId.toString());
        reqMap.put("poNo", trfNo);
        String fullJSON = null;
        try {
            fullJSON = objectMapper.writeValueAsString(reqMap);
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(url, reqMap, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            return JSONArray.parseObject(JSONPath.eval(response, "$.data.orderList[0]").toString(), PoResult.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public PoResult queryPoData(Integer refSystemId, String trfNo, String productLineCode) {
        SyncTrfInfoReq req = new SyncTrfInfoReq();
        req.setRefSystemId(refSystemId);
        req.setProductLineCode(productLineCode);
        req.setTrfNo(trfNo);
        req.setAction("GetDollarTreePOTrfInfo");
        String url = String.format("%s/openapi/sync/getInfo", interfaceConfig.getLocaliLayerUrl());
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(url, req, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            return JSONArray.parseObject(JSONPath.eval(response, "$.data.detail.orderList[0]").toString(), PoResult.class);
        } catch (Exception e) {
            return null;
        }
    }

    public CustomResult<List<CheckTestLineMappingRsp>> checkTestLineMappingExists(CheckTestLineMappingReq reqObject) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/checkTestLineMappingExists");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                List<CheckTestLineMappingRsp> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), CheckTestLineMappingRsp.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.checkTestLineMappingExists param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    public CustomResult<List<NewCheckTestLineMappingRsp>> checkTestLineMappingExists(NewCheckTestLineMappingReq reqObject) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/checkTestLineMappingExists");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                List<NewCheckTestLineMappingRsp> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), NewCheckTestLineMappingRsp.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.checkTestLineMappingExists param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    public CustomResult<QueryTestLineMappingRsp> queryTestLineMapping(QueryTestLineMappingReq queryTestLineMappingReq) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/queryTestLineMapping");
        try {
            QueryTestLineMappingRsp response = HttpClientUtil.post(localiLayerUrl, queryTestLineMappingReq, QueryTestLineMappingRsp.class);
            if (Func.isNotEmpty(response)) {
                customResult.setData(response);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.queryTestLineMapping param({})  请求失败:{}", JSONObject.toJSONString(queryTestLineMappingReq), "request error");
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    public CustomResult<List<CheckTestLineMappingExistsDTO>> queryTestLineMappingExists(CheckTestLineMappingReq reqObject) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/checkTestLineMappingExists");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                List<CheckTestLineMappingExistsDTO> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), CheckTestLineMappingExistsDTO.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.checkTestLineMappingExists param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    public <T> CustomResult<List<T>> queryTestLineMappingExists(CheckTestLineMappingReq reqObject, Class<T> clazz) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/checkTestLineMappingExists");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, reqObject, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                List<T> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), clazz);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.checkTestLineMappingExists param({})  请求失败:{}", JSONObject.toJSONString(reqObject), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }


    public CustomResult<List<GetBasicDataMappingRsp>> getBasicDataMapping(MappingBasicDataReq mappingBasicDataReq) {
        CustomResult customResult = new CustomResult(false);

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/getBasicDataMapping");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, mappingBasicDataReq, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                List<GetBasicDataMappingRsp> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), GetBasicDataMappingRsp.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.queryTestLineMapping param({})  请求失败:{}", JSONObject.toJSONString(mappingBasicDataReq), "request error");
            return customResult;
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    public CustomResult<List<StarLimsFieldMapping>> queryStarLimsFieldMapping(QueryStarLimsFieldMapping query) {
        CustomResult<List<StarLimsFieldMapping>> customResult = new CustomResult<>(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/func/queryDFFAndStarlimsFieldMapping");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = HttpClientUtil.post(localiLayerUrl, query, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (Objects.nonNull(response) && response.getStatus() == 200) {
                List<StarLimsFieldMapping> checkTestLineMappingRsps = JSONObject.parseArray(response.getData().toString(), StarLimsFieldMapping.class);
                customResult.setData(checkTestLineMappingRsps);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.queryDFFAndStarlimsFieldMapping param({})  请求失败:{}", JSONObject.toJSONString(query), "request error");
            customResult.setMsg((Objects.nonNull(response) ? response.getMessage() : "") + " error From LocalILayer" + localiLayerUrl);
            return customResult;
        } catch (Exception e) {
            logger.error("{} got an error. param is : {}", localiLayerUrl, JSONObject.toJSONString(query), e);
            customResult.setMsg(localiLayerUrl + " got an error. param is : " + JSONObject.toJSONString(query));
            return customResult;
        }

    }


    public CustomResult<QueryDffDefaultRsp> queryDFFDefault(DffInfoReq dffInfoReq) {
        CustomResult customResult = new CustomResult(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getBaseUrl(), "DFFV2Api/dffMapping/queryDFFDefault");
        try {
            com.sgs.otsnotes.facade.model.common.BaseResponse response = com.sgs.framework.tool.utils.HttpClientUtil.post(localiLayerUrl, dffInfoReq, com.sgs.otsnotes.facade.model.common.BaseResponse.class);
            if (response.getStatus() == 200) {
                QueryDffDefaultRsp queryDffDefaultRsp = JSONObject.parseObject(response.getData().toString(), QueryDffDefaultRsp.class);
                customResult.setData(queryDffDefaultRsp);
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.queryDFFDefault param({})  请求失败:{}", JSONObject.toJSONString(dffInfoReq), response.getMessage());
            customResult.setMsg(response.getMessage());
            return customResult;
        } catch (Exception e) {
            logger.error("queryDFFDefault failure.{}", e);
        }
        return customResult;
    }

    public List<DFFMappingDTO> queryDFFMappingConfig(Integer refSystemId, String bu, String customerGroup, String customerNo, String templateId, String templateType) {

        DFFMappingQuery query = new DFFMappingQuery();
        query.setIdentityId(String.valueOf(refSystemId));
        query.setProductLine(bu);
        query.setCustomerGroup(customerGroup);
        query.setCustomerNo(customerNo);
        if(!RefSystemIdEnum.check(refSystemId, RefSystemIdEnum.LULULEMON_INSPECTORIO)) {
            query.setTemplateId(templateId);
        }
        query.setTemplateType(templateType);

        List<DFFMappingDTO> dffMappingList = dffMappingService.getDFFMapping(query);
        if (Func.isEmpty(dffMappingList)) {
            query.setProductLine(null);
            query.setTemplateId(null);
            query.setTemplateType(null);
            dffMappingList = dffMappingService.getDFFMapping(query);
        }

        /**
         * 做一个检查（根据上述查询条件不能查询两套数据）
         */
//        Map<String, List<DFFMappingDTO>> labCodeLevelProperties = dffMappingList.stream().collect(Collectors.groupingBy(DFFMappingDTO::getLabelCode));
//
//        boolean hasDuplicateItem = labCodeLevelProperties.values().stream().anyMatch(mappinglist -> mappinglist.size() > 1);
//
//        if (hasDuplicateItem){
//            //如果有相同的属性存在两条配置记录，则认为是错误
//            logger.error("DFF 配置存在记录冲突，refSystemId:{},bu:{},customerGroup:{},customerNo:{}", refSystemId, bu, customerGroup, customerNo);
//            return null;
//        }

        return dffMappingList;
    }

    public List<DFFMappingDTO> queryDFFMappingConfig(Integer refSystemId, String bu, String customerGroup, String customerNo) {

        ConfigGetReq configGetReq = new ConfigGetReq();
        configGetReq.setIdentityId(String.valueOf(refSystemId));
        configGetReq.setProductLine(bu);
        configGetReq.setCustomerGroup(customerGroup);
        configGetReq.setCustomerNo(customerNo);

        List<DFFMappingDTO> dffMappingList = dffMappingService.getDFFMapping(configGetReq);
        if (Func.isEmpty(dffMappingList)) {
            configGetReq.setProductLine(null);
            dffMappingList = dffMappingService.getDFFMapping(configGetReq);
        }

        /**
         * 做一个检查（根据上述查询条件不能查询两套数据）
         */
//        Map<String, List<DFFMappingDTO>> labCodeLevelProperties = dffMappingList.stream().collect(Collectors.groupingBy(DFFMappingDTO::getLabelCode));
//
//        boolean hasDuplicateItem = labCodeLevelProperties.values().stream().anyMatch(mappinglist -> mappinglist.size() > 1);
//
//        if (hasDuplicateItem){
//            //如果有相同的属性存在两条配置记录，则认为是错误
//            logger.error("DFF 配置存在记录冲突，refSystemId:{},bu:{},customerGroup:{},customerNo:{}", refSystemId, bu, customerGroup, customerNo);
//            return null;
//        }

        return dffMappingList;
    }


    public List<SystemLabInfoRsp.SystemLabInfoRow> querySystemLabInfo(SystemLabInfoReq systemLabInfoReq) {

        String localiLayerUrl = String.format("%s%s", interfaceConfig.getFrameWorkApiUrl(), "/busetting/get");
        try {
            SystemLabInfoRsp post = com.sgs.framework.tool.utils.HttpClientUtil.post(localiLayerUrl, systemLabInfoReq, SystemLabInfoRsp.class);
            if (Func.isNotEmpty(post) && post.getIsSuccess()) {
                return post.getRows();
            }
            logger.error("LocaliLayerClient.querySystemLabInfo param({})  请求失败:{}", JSONObject.toJSONString(systemLabInfoReq), post.getErrorMessage());
        } catch (Exception e) {
            logger.error("queryDFFDefault failure.{}", e);
        }
        return null;
    }
    public CustomResult queryCustomerTrfList(JSON boundTrfInfoSearchReq) {
        CustomResult customResult = new CustomResult<>(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/getInspectorioLabTestList");
        try {
            String jsonStr = post(localiLayerUrl, boundTrfInfoSearchReq);
            if (Func.isNotBlank(jsonStr)) {
                BaseResponse baseResponses = JSONObject.parseObject(jsonStr, BaseResponse.class);
                if (Func.isNotEmpty(baseResponses) && baseResponses.getStatus() == 200) {
                    customResult.setSuccess(true);
                    Object data = baseResponses.getData();
                    customResult.setData(data);
                    return customResult;
                }
            }
            logger.error("LocaliLayerClient.queryCustomerTrfList param({})  请求失败:{}", JSONObject.toJSONString(boundTrfInfoSearchReq), "request error");
        } catch (Exception e) {
            logger.error("Get trf no failure.{}", e);
        }
        return customResult;
    }

    /**
     * 获取Inspectorio实验室测试信息
     * @param action 操作类型，例如 "GetTestPackageList"
     * @param refSystemId 系统ID
     * @param offset 偏移量
     * @param limit 限制数量
     * @return CustomResult<List<T>>
     */
    public CustomResult<ResultData> getInspectorioLabTestInfo(String action, Integer refSystemId,
                                                              Integer offset, Integer limit) {
        CustomResult<ResultData> customResult = new CustomResult<>(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/getInspectorioLabTestInfo");
        
        // 构建请求参数
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("action", action);
        reqParams.put("refSystemId", String.valueOf(refSystemId));
        reqParams.put("offset", offset);
        reqParams.put("limit", limit);
        
        try {
            String result = HttpClientUtil.postJson(localiLayerUrl, reqParams);
            BaseResponse<ResultData> response = JSONObject.parseObject(result, new com.alibaba.fastjson.TypeReference<BaseResponse<ResultData>>(){});

            if (Objects.nonNull(response) && Objects.equals(response.getStatus(), 200)) {
                customResult.setData(response.getData());
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.getInspectorioLabTestInfo param({})  请求失败:{}", JSONObject.toJSONString(reqParams), 
                Objects.nonNull(response) ? response.getMessage() : "response is null");
            customResult.setMsg(Objects.nonNull(response) ? response.getMessage() : "response is null");
            return customResult;
        } catch (Exception e) {
            logger.error("getInspectorioLabTestInfo failure. param: {}", JSONObject.toJSONString(reqParams), e);
            customResult.setMsg("getInspectorioLabTestInfo failure: " + e.getMessage());
        }
        return customResult;
    }

    /**
     * 获取Inspectorio实验室测试信息
     * @param action 操作类型，例如 "GetTestPackageList"
     * @param refSystemId 系统ID
     * @return CustomResult<List<T>>
     */
    public CustomResult<JSONObject> getInspectorioLabTestDetailInfo(String action, Integer refSystemId, String id) {
        CustomResult<JSONObject> customResult = new CustomResult<>(false);
        String localiLayerUrl = String.format("%s%s", interfaceConfig.getLocaliLayerUrl(), "/openapi/sync/getInspectorioLabTestInfo");

        // 构建请求参数
        Map<String, Object> reqParams = new HashMap<>();
        reqParams.put("action", action);
        reqParams.put("refSystemId", String.valueOf(refSystemId));
        reqParams.put("id", id);

        try {
            String result = HttpClientUtil.postJson(localiLayerUrl, reqParams);
            BaseResponse<JSONObject> response = JSONObject.parseObject(result, new com.alibaba.fastjson.TypeReference<BaseResponse<JSONObject>>(){});

            if (Objects.nonNull(response) && Objects.equals(response.getStatus(), 200)) {
                customResult.setData(response.getData());
                customResult.setSuccess(true);
                return customResult;
            }
            logger.error("LocaliLayerClient.getInspectorioLabTestInfo param({})  请求失败:{}", JSONObject.toJSONString(reqParams),
                    Objects.nonNull(response) ? response.getMessage() : "response is null");
            customResult.setMsg(Objects.nonNull(response) ? response.getMessage() : "response is null");
            return customResult;
        } catch (Exception e) {
            logger.error("getInspectorioLabTestInfo failure. param: {}", JSONObject.toJSONString(reqParams), e);
            customResult.setMsg("getInspectorioLabTestInfo failure: " + e.getMessage());
        }
        return customResult;
    }

    public static String post(String url, Object reqObject) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);
        String result = "";
        try {
            // 设置请求体
            StringEntity entity = new StringEntity(reqObject.toString(), "UTF-8");
            entity.setContentType("application/json");
            httpPost.setEntity(entity);

            // 执行请求
            CloseableHttpResponse response = httpClient.execute(httpPost);
            try {
                // 获取响应实体
                HttpEntity responseEntity = response.getEntity();
                if (responseEntity != null) {
                    result = EntityUtils.toString(responseEntity, "UTF-8");
                }
                EntityUtils.consume(responseEntity);
            } finally {
                response.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return result;
    }
}
