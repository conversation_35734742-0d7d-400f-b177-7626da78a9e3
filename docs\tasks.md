# SCI-1842 Inspectorio TRF TODO List 详细任务列表

## 任务概述
根据设计方案，将实现分为以下几个阶段：数据库设计、数据访问层、业务逻辑层、调度任务层、Web接口层、配置和部署。

## 第一阶段：数据库设计和数据访问层

### 任务1.1：创建数据库表
**任务描述**：创建`inspectorio_trf_info`表，用于存储Inspectorio TRF数据
**具体工作**：
- 编写DDL脚本创建表结构
- 创建必要的索引（主键、唯一键、普通索引）
- 添加表注释和字段注释
- 在测试环境执行并验证

**文件路径**：`customerbiz-dbstorages/src/main/resources/sql/create_inspectorio_trf_info.sql`

### 任务1.2：创建PO实体类
**任务描述**：创建与数据库表对应的PO实体类
**具体工作**：
- 创建`InspectorioTrfInfoPO`类
- 添加所有字段属性和getter/setter方法
- 使用Lombok注解简化代码
- 添加必要的注释

**文件路径**：`customerbiz-dbstorages/src/main/java/com/sgs/customerbiz/dbstorages/mybatis/model/InspectorioTrfInfoPO.java`

### 任务1.3：创建Mapper接口
**任务描述**：创建MyBatis Mapper接口，定义数据库操作方法
**具体工作**：
- 创建`InspectorioTrfInfoMapper`接口
- 定义基础CRUD方法
- 定义批量操作方法
- 定义按条件查询方法

**文件路径**：`customerbiz-dbstorages/src/main/java/com/sgs/customerbiz/dbstorages/mybatis/mapper/InspectorioTrfInfoMapper.java`

### 任务1.4：创建Mapper XML文件
**任务描述**：编写MyBatis XML映射文件，实现SQL语句
**具体工作**：
- 创建对应的XML映射文件
- 实现所有Mapper接口方法的SQL
- 优化查询性能，使用合适的索引
- 实现分页查询逻辑

**文件路径**：`customerbiz-dbstorages/src/main/resources/mapper/InspectorioTrfInfoMapper.xml`

### 任务1.5：创建Example类
**任务描述**：创建查询条件构建类，支持动态查询
**具体工作**：
- 创建`InspectorioTrfInfoExample`类
- 实现动态查询条件构建
- 支持多字段组合查询
- 支持排序和分页

**文件路径**：`customerbiz-dbstorages/src/main/java/com/sgs/customerbiz/dbstorages/mybatis/model/InspectorioTrfInfoExample.java`

## 第二阶段：业务逻辑层

### 任务2.1：扩展InspectorioDataBizService
**任务描述**：在现有的InspectorioDataBizService中添加TRF同步功能
**具体工作**：
- 添加`syncLululemonTrfInfo`方法
- 添加`syncTargetTrfInfo`方法
- 实现数据同步核心逻辑
- 添加时间范围参数解析功能
- 实现批量数据处理逻辑

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/service/inspectorio/InspectorioDataBizService.java`

### 任务2.2：创建InspectorioTrfBizService
**任务描述**：创建专门的TRF业务服务类，处理TRF相关业务逻辑
**具体工作**：
- 创建服务类和接口
- 实现TRF数据查询功能
- 实现数据转换和处理逻辑
- 添加缓存支持
- 实现分页查询功能

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/service/InspectorioTrfBizService.java`

### 任务2.3：创建参数对象类
**任务描述**：创建同步参数和查询参数对象类
**具体工作**：
- 创建`InspectorioSyncParam`类
- 创建`InspectorioTrfQueryParam`类
- 添加参数验证逻辑
- 实现时间范围解析功能

**文件路径**：`customerbiz-model/src/main/java/com/sgs/customerbiz/model/inspectorio/`

### 任务2.4：实现数据转换逻辑
**任务描述**：实现Inspectorio API响应数据到本地数据库的转换
**具体工作**：
- 解析JSON响应数据
- 提取关键字段（uuid、labTestId、status等）
- 处理日期格式转换
- 实现数据验证逻辑

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/convert/InspectorioTrfConverter.java`

## 第三阶段：调度任务层

### 任务3.1：创建Lululemon TRF同步调度器
**任务描述**：创建XXL-Job调度器，定时同步Lululemon TRF数据
**具体工作**：
- 创建`SyncLululemonTrfInfoXXLJobScheduler`类
- 实现`@XxlJob`注解的处理方法
- 添加参数解析逻辑
- 实现错误处理和日志记录
- 添加任务执行统计

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/service/task/impl/SyncLululemonTrfInfoXXLJobScheduler.java`

### 任务3.2：创建Target TRF同步调度器
**任务描述**：创建XXL-Job调度器，定时同步Target TRF数据
**具体工作**：
- 创建`SyncTargetTrfInfoXXLJobScheduler`类
- 复用Lululemon调度器的逻辑
- 配置Target系统的特定参数
- 实现独立的错误处理

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/service/task/impl/SyncTargetTrfInfoXXLJobScheduler.java`

### 任务3.3：创建Job参数解析工具
**任务描述**：创建通用的Job参数解析工具类
**具体工作**：
- 实现JSON参数解析
- 支持相对时间和绝对时间解析
- 添加参数验证功能
- 实现默认值设置

**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/utils/JobParamParser.java`

## 第四阶段：Web接口层

### 任务4.1：创建手动同步控制器
**任务描述**：创建REST接口，支持前端手动触发同步
**具体工作**：
- 在`TodoListController`实现`manualSync`接口方法
- 添加请求参数验证
- 实现响应数据封装
- 添加接口文档注释

**文件路径**：`customerbiz-web/src/main/java/com/sgs/customerbiz/web/controllers/TodoListController.java`

### 任务4.2：创建请求响应对象
**任务描述**：创建接口的请求和响应对象类
**具体工作**：
- 创建`ManualSyncRequest`类
- 创建`ManualSyncResponse`类
- 添加参数验证注解
- 实现序列化支持

**文件路径**：`customerbiz-model/src/main/java/com/sgs/customerbiz/model/trf/dto/req/ManualSyncRequest.java`


## 第五阶段：配置和集成

### 任务5.1：添加应用配置
**任务描述**：在应用配置文件中添加新功能的配置项
**具体工作**：
- 添加功能开关配置
**文件路径**：`customerbiz-biz/src/main/java/com/sgs/customerbiz/biz/config/TrfBizConfig.java`



## 第七阶段：部署和上线

### 任务7.1：准备部署脚本
**任务描述**：准备数据库变更和应用部署脚本
**具体工作**：
- 编写数据库DDL脚本
- 编写数据迁移脚本（如需要）
- 准备配置文件更新
- 编写部署文档

### 任务7.2：配置XXL-Job任务
**任务描述**：在XXL-Job管理平台配置定时任务
**具体工作**：
- 创建Lululemon TRF同步任务
- 创建Target TRF同步任务
- 配置执行频率（每小时执行）
- 设置任务参数和告警

### 任务7.3：生产环境验证
**任务描述**：在生产环境验证功能正确性
**具体工作**：
- 验证数据同步功能
- 验证手动同步接口
- 验证查询功能
- 监控系统性能和稳定性

## 风险和注意事项

1. **数据一致性**：确保同步过程中的数据一致性，避免重复或丢失
2. **性能影响**：大批量数据同步可能影响数据库性能，需要合理控制批次大小
3. **API限制**：注意Inspectorio API的调用频率限制
4. **向后兼容**：确保新功能不影响现有的TRF TODO List功能
5. **错误处理**：完善的异常处理和重试机制
6. **监控告警**：及时发现和处理同步异常