# SCI-1842 Inspectorio TRF TODO List 设计方案

## 1. 架构设计

### 1.1 整体架构变化
在现有SCI系统基础上，新增Inspectorio TRF数据同步模块：

```
┌─────────────────────────────────────────────────────────────┐
│                    SCI系统架构                                │
├─────────────────────────────────────────────────────────────┤
│  Web层: TrfSyncController (手动同步接口)                      │
├─────────────────────────────────────────────────────────────┤
│  业务层: InspectorioTrfBizService                            │
│         - syncLululemonTrfInfo()                            │
│         - syncTargetTrfInfo()                               │
│                             │
├─────────────────────────────────────────────────────────────┤
│  调度层: XXL-Job                                             │
│         - SyncLululemonTrfInfoXXLJobScheduler               │
│         - SyncTargetTrfInfoXXLJobScheduler                  │
├─────────────────────────────────────────────────────────────┤
│  数据层: inspectorio_trf_info表                              │
├─────────────────────────────────────────────────────────────┤
│  集成层: iLayerClient (调用Inspectorio接口)                   │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 数据流向
1. **定时同步**: XXL-Job → InspectorioTrfBizService → iLayerClient → Inspectorio API
2. **手动同步**: 前端 → TrfSyncController → InspectorioTrfBizService → iLayerClient → Inspectorio API

## 2. 技术栈

- **编程语言**: Java 8+
- **框架**: Spring Boot, MyBatis
- **定时任务**: XXL-Job
- **数据库**: MySQL 8.0
- **JSON处理**: FastJSON
- **HTTP客户端**: 复用现有iLayerClient

## 3. 数据库设计

### 3.1 表结构设计

```sql
CREATE TABLE `tb_inspectorio_trf_info` (
  `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `uuid` varchar(64) NOT NULL COMMENT 'Customer UUID (uuid)',
  `trf_no` varchar(128) NOT NULL COMMENT 'Customer TRF No (labTestId)',
  `ref_system_id` int(11) NOT NULL COMMENT '系统ID(10030=Lululemon, 10028=Target)',
  `created_at` datetime COMMENT 'Customer createdAt',
  `updated_at` datetime COMMENT 'Customer updatedAt', 
  `trf_status` varchar(32) COMMENT 'Customer status',
  `raw_data` json NOT NULL COMMENT '原始JSON数据',
  `active_indicator` tinyint NOT NULL DEFAULT '1',
  `created_date` datetime DEFAULT CURRENT_TIMESTAMP,
  `modified_date` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_lab_test_id_ref_system` (`ref_system_id`,`trf_no`),
  KEY `idx_uuid` (`uuid`),
  KEY `idx_created_date` (`created_at`),
  KEY `idx_updated_date` (`updated_at`),
  KEY `idx_status` (`trf_status`)
) COMMENT='Inspectorio TRF信息表';
```

### 3.2 字段说明
- `lab_test_id`: 外部主键，用于数据更新判断
- `ref_system_id`: 区分Lululemon(10030)和Target(10028)
- `raw_data`: 存储完整的JSON响应数据
- `uuid`, `created_date`, `updated_date`, `status`: 提取的高频查询字段

## 4. 需求功能设计

### 4.1 定时同步功能

#### 4.1.1 时序图
```mermaid
sequenceDiagram
    participant XXL as XXL-Job
    participant Biz as InspectorioTrfBizService
    participant Client as iLayerClient
    participant API as Inspectorio API
    participant DB as Database

    XXL->>Biz: 触发定时任务(参数配置)
    Biz->>Biz: 解析时间范围参数
    Biz->>Client: 调用queryCustomerTrfList
    Client->>API: HTTP请求(updatedFrom, updatedTo)
    API-->>Client: 返回TRF列表
    Client-->>Biz: 返回数据
    Biz->>Biz: 数据转换和处理
    Biz->>DB: 批量插入/更新
    DB-->>Biz: 操作结果
    Biz-->>XXL: 任务完成
```

#### 4.1.2 状态图
```mermaid
stateDiagram-v2
    [*] --> 解析参数
    解析参数 --> 计算时间范围
    计算时间范围 --> 调用API
    调用API --> 数据处理
    数据处理 --> 数据库操作
    数据库操作 --> 任务完成
    任务完成 --> [*]
    
    调用API --> API异常: 接口调用失败
    API异常 --> 任务失败
    任务失败 --> [*]
    
    数据库操作 --> DB异常: 数据库操作失败
    DB异常 --> 任务失败
```

### 4.2 手动同步功能

#### 4.2.1 类图
```mermaid
classDiagram
    class TrfSyncController {
        +manualSync(request): BaseResponse
    }
    
    class ManualSyncRequest {
        +Integer refSystemId
        +String timeRange
    }
    
    class InspectorioTrfBizService {
        +syncLululemonTrfInfo(param): void
        +syncTargetTrfInfo(param): void
    }
    
    class InspectorioSyncParam {
        +Integer sizeOfApi
        +Integer sizeOfDb
        +String timeRange
        +String updatedFrom
        +String updatedTo
        +Integer refSystemId
    }
    
    TrfSyncController --> ManualSyncRequest
    TrfSyncController --> InspectorioTrfBizService
    InspectorioTrfBizService --> InspectorioSyncParam
```

### 4.3 查询功能

#### 4.3.1 查询流程
```mermaid
flowchart TD
    A[前端查询请求] --> B[TodoListService]
    B --> C{查询模式判断}
    C -->|本地模式| D[现有逻辑]
    C -->|远程模式| E{是否使用同步表查询}
    E --> |Y| E1[inspectorio_trf_info join tb_trf join tb_trf_order]
    E --> |N| E2[现有远程查询逻辑]
    D --> F[数据库查询]
    F --> G[结果返回]
    E1 --> G[结果返回]
    E2 --> G
    G --> H[前端展示]
```

## 5. 接口设计

### 5.1 手动同步接口

**Endpoint**: `POST /api/trf/manual-sync`

**请求参数**:
```json
{
  "refSystemId": 10030,
  "timeRange": "1h"
}
```

**响应示例**:
```json
{
  "success": true,
  "code": "200",
  "message": "同步完成",
  "data": {
    "syncCount": 150,
    "updateCount": 30,
    "insertCount": 120
  }
}
```

**错误码**:
- `400`: 参数错误
- `500`: 系统内部错误
- `503`: Inspectorio服务不可用


## 6. 配置参数设计

### 6.1 XXL-Job参数配置
```json
{
  "sizeOfApi": 50,
  "sizeOfDb": 100,
  "maxSizeOfApi": 1000,
  "timeRange": "1h",
  "updatedFrom": "2023-07-01", 
  "updatedTo": "2023-07-07"
  "refSystemId": 10030
}
```

### 6.2 应用配置
```properties
# Inspectorio TRF同步配置
inspectorio.trf.query.usingLocalTrfInfo=true
```

## 8. 监控和日志

### 8.1 关键监控指标
- 同步任务执行时间
- 同步数据量统计
- API调用成功率
- 数据库操作性能

### 8.2 日志记录
- 同步任务开始/结束时间
- 数据处理异常信息
- API调用详细日志
- 数据库操作日志

## 9. 部署和配置

### 9.1 数据库变更
1. 执行DDL脚本创建`inspectorio_trf_info`表

### 9.2 XXL-Job配置
1. 创建Lululemon TRF同步任务
2. 创建Target TRF同步任务
3. 配置执行频率和参数

### 9.3 应用配置
1. 更新application.properties配置
2. 启用新功能开关